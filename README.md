# Portfolio Website

A modern, responsive portfolio website showcasing my projects and skills as a software engineer.

## Features

- **Dynamic GitHub Integration**: Automatically fetches and displays selected repositories
- **Responsive Design**: Works seamlessly across all devices
- **Contact Form**: Interactive form with validation
- **Smooth Navigation**: Scroll-to-section functionality
- **Modern UI**: Dark theme with teal accents

## Technologies Used

- HTML5
- CSS3 (Tailwind CSS)
- Vanilla JavaScript
- GitHub API

## Setup

1. Clone the repository
2. Open `index.html` in your browser
3. To customize projects, edit the `selectedRepos` array in `script.js`

## Live Demo

Visit the live site: [Your Portfolio URL]

## Contact

- GitHub: [@denilany](https://github.com/denilany)
- Twitter: [@denil_dev](https://twitter.com/denil_dev)
- LinkedIn: [Denil Anyonyi](https://www.linkedin.com/in/denil-anyonyi/)